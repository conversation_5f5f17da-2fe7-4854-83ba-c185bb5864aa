from odoo import models, fields, api


class BudgetReport(models.Model):
    _inherit = 'budget.report'

    payment_method_id = fields.Many2one(
        'account.payment.method',
        string='Méthode de paiement',
        compute='_compute_payment_method_id'
    )

    @api.depends('res_model', 'res_id')
    def _compute_payment_method_id(self):
        for record in self:
            payment_method = False

            # Only compute for analytic line records
            if record.res_model == 'account.analytic.line' and record.res_id:
                analytic_line = self.env['account.analytic.line'].browse(record.res_id)

                if analytic_line.exists() and analytic_line.move_id:
                    move = analytic_line.move_id
                    payment = self.env['account.payment'].search([
                        ('move_id', '=', move.id)
                    ], limit=1)

                    if payment:
                        payment_method = payment.payment_method_line_id.payment_method_id

            record.payment_method_id = payment_method
