from odoo import models, fields, api


class BudgetReport(models.Model):
    _inherit = 'budget.report'

    analytic_line_id = fields.Many2one(
        'account.analytic.line',
        string='Ligne analytique'
    )

    payment_method_id = fields.Many2one(
        'account.payment.method',
        string='Méthode de paiement',
        compute='_compute_payment_method_id',
        store=True
    )

    @api.depends('analytic_line_id')
    def _compute_payment_method_id(self):
        for record in self:
            payment_method = False
            analytic_line = record.analytic_line_id

            if analytic_line and analytic_line.move_id:
                move = analytic_line.move_id
                payment = self.env['account.payment'].search([
                    ('move_id', '=', move.id)
                ], limit=1)

                if payment:
                    payment_method = payment.payment_method_line_id.payment_method_id

            record.payment_method_id = payment_method
