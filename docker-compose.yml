services:
  db19:
    image: postgres:14
    container_name: odoo19-db
    environment:
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      POSTGRES_DB: odoo19
    volumes:
      - ./postgresql:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - odoo19_network

  odoo19:
    build: .
    container_name: odoo19
    depends_on:
      - db19
    ports:
      - "8052:8069"
    volumes:
      - ./odoo:/mnt/odoo                # Community code (host → container)
      - ./enterprise:/mnt/enterprise-addons:ro   # Enterprise addons
      - ./custom-addons:/mnt/custom-addons       # Your addons
      - ./odoo.conf:/etc/odoo/odoo.conf:ro       # Config
      - odoo_data19:/var/lib/odoo                # Persistent filestore
    command: >
      python3 /mnt/odoo/odoo-bin -c /etc/odoo/odoo.conf
    restart: unless-stopped
    networks:
      - odoo19_network

volumes:
  odoo_data19:

networks:
  odoo19_network:
    driver: bridge
