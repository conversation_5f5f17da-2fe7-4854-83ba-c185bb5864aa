# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_gantt
#
# Translators:
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-06-03 09:04+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> <dragan.vukosa<PERSON><PERSON><PERSON><PERSON>@gmail.com>, "
"2022\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"Language: sr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s is on time off %(leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(name)s requested time off %(leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr "dana"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr "Odsustvo"

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "from %(start_date)s to %(end_date)s"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "on %(start_date)s from %(start_time)s to %(end_time)s"
msgstr ""
