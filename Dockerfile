FROM python:3.11-slim-bookworm

# Locale
ENV LANG=C.UTF-8 LC_ALL=C.UTF-8

# System packages (build tools, postgres client, XML libs, etc.) + wkhtmltopdf for PDF reports
RUN apt-get update && apt-get install -y --no-install-recommends \
    git wget curl \
    node-less npm \
    python3-dev \
    libxml2-dev libxslt1-dev \
    libldap2-dev libsasl2-dev \
    libpq-dev \
    gcc g++ make \
    xfonts-75dpi xfonts-base \
    fontconfig libxrender1 libjpeg62-turbo libssl3 \
    wkhtmltopdf \
 && rm -rf /var/lib/apt/lists/*

# Non-root user + persistent filestore dir
RUN useradd -ms /bin/bash odoo \
 && mkdir -p /var/lib/odoo \
 && chown -R odoo:odoo /var/lib/odoo

# Install Odoo Python deps
# - We copy requirements.txt from your cloned Odoo 19 repo on the host
COPY ./odoo/requirements.txt /tmp/requirements.txt
RUN pip install --upgrade pip setuptools wheel cython \
 && pip install --no-cache-dir -r /tmp/requirements.txt \
 && pip install --no-cache-dir \
    babel \
    psycopg2-binary \
    Werkzeug==2.3.7 \
    phonenumbers \
    qrcode \
    reportlab \
    passlib \
    num2words \
    polib

USER odoo
WORKDIR /opt/odoo

# Default Odoo port
EXPOSE 8069

# We run Odoo from the source you mount at /mnt/odoo (via docker-compose)
CMD ["python3", "/mnt/odoo/odoo-bin", "-c", "/etc/odoo/odoo.conf"]
